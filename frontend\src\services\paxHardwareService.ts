/**
 * PAX Hardware Service
 * Direct integration with PAX C# DLL for local hardware communication
 * This service runs a local Node.js bridge to communicate with the C# libraries
 */

import { env } from '../config/env';

export interface PaxHardwareConfig {
  dllPath: string;
  terminalIP: string;
  terminalPort: number;
  timeout: number;
  merchantId: string;
  authUrl: string;
  localBridgeUrl: string;
}

export interface PaxHardwarePaymentRequest {
  amount: number; // Amount in cents
  tenderType: 'CREDIT' | 'DEBIT' | 'CASH';
  transType: 'SALE' | 'REFUND' | 'VOID';
  referenceNumber?: string;
  items?: Array<{
    name: string;
    price: number;
    quantity: number;
  }>;
}

export interface PaxHardwarePaymentResponse {
  success: boolean;
  transactionId?: string;
  authCode?: string;
  resultCode?: string;
  message?: string;
  receiptData?: string;
  cardInfo?: {
    last4?: string;
    brand?: string;
    entryMethod?: string;
  };
  rawResponse?: any;
}

export interface PaxHardwareStatus {
  connected: boolean;
  dllLoaded: boolean;
  terminalReachable: boolean;
  lastError?: string;
  capabilities?: {
    contactless: boolean;
    emv: boolean;
    magneticStripe: boolean;
    printer: boolean;
  };
}

class PaxHardwareService {
  private bridgePort: number = 3002;
  private bridgeUrl: string;
  private isInitialized: boolean = false;
  private bridgeProcess: any = null;
  private config: PaxHardwareConfig;

  constructor() {
    this.bridgeUrl = `http://localhost:${this.bridgePort}`;
    this.config = {
      dllPath: '/libs/PaxWrapperSDK.dll',
      terminalIP: '**************', // From your terminal info
      terminalPort: 10009,
      timeout: 90,
      merchantId: 'MERCHANT001',
      authUrl: '',
      localBridgeUrl: this.bridgeUrl
    };

    // PAX bridge service disabled - manual start required
    console.log('PAX hardware service initialized but bridge auto-start is disabled');
    console.log('To enable PAX functionality, manually start the bridge service');
  }

  /**
   * Initialize the PAX hardware service
   * Connects to local bridge server that communicates with PAX hardware
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('Initializing PAX hardware service...');

      // Check if bridge server is running locally
      const bridgeRunning = await this.checkBridgeStatus();
      if (!bridgeRunning) {
        console.warn('PAX bridge server not running locally.');
        console.warn('Please start the bridge server: cd local-bridge && npm start');
        return false;
      }

      console.log('Bridge server detected, initializing terminal connection...');

      // Initialize terminal connection through bridge
      const response = await fetch(`${this.bridgeUrl}/initialize`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Terminal initialization failed: ${errorData.error || response.statusText}`);
      }

      const result = await response.json();
      this.isInitialized = result.success;

      if (this.isInitialized) {
        console.log(`PAX terminal initialized successfully at ${result.terminalIP || this.config.terminalIP}`);
      }

      return this.isInitialized;
    } catch (error) {
      console.error('Failed to initialize PAX Hardware Service:', error);
      return false;
    }
  }

  /**
   * Check if running on Windows
   */
  private isWindowsEnvironment(): boolean {
    return navigator.userAgent.toLowerCase().includes('windows');
  }

  /**
   * Start the local bridge service (available for manual use when PAX is enabled)
   */
  async startBridgeService(): Promise<void> {
    try {
      // Check if bridge is already running
      const isRunning = await this.checkBridgeStatus();
      if (isRunning) {
        console.log('PAX bridge service already running');
        return;
      }

      // Start the bridge service using Electron or Node.js child process
      if (this.isElectronEnvironment()) {
        await this.startElectronBridge();
      } else {
        await this.startWebBridge();
      }

    } catch (error) {
      throw new Error(`Failed to start bridge service: ${error.message}`);
    }
  }

  /**
   * Check if running in Electron environment
   */
  private isElectronEnvironment(): boolean {
    return !!(window as any).require;
  }

  /**
   * Start bridge in Electron environment
   */
  private async startElectronBridge(): Promise<void> {
    const { spawn } = (window as any).require('child_process');
    const path = (window as any).require('path');

    const bridgeScript = path.join(process.cwd(), 'frontend', 'src', 'services', 'paxBridge.js');
    
    this.bridgeProcess = spawn('node', [bridgeScript, '--port', this.bridgePort.toString()], {
      stdio: 'pipe',
      detached: false
    });

    // Wait for bridge to start
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => reject(new Error('Bridge startup timeout')), 10000);
      
      const checkStartup = async () => {
        try {
          const isRunning = await this.checkBridgeStatus();
          if (isRunning) {
            clearTimeout(timeout);
            resolve(true);
          } else {
            setTimeout(checkStartup, 500);
          }
        } catch (error) {
          setTimeout(checkStartup, 500);
        }
      };

      checkStartup();
    });
  }

  /**
   * Start bridge in web environment (requires user to manually start bridge)
   */
  private async startWebBridge(): Promise<void> {
    // In web environment, we can't start processes directly
    // User needs to manually start the bridge service
    const isRunning = await this.checkBridgeStatus();
    if (!isRunning) {
      throw new Error(
        'PAX Bridge Service not running. Please start it manually:\n' +
        'cd frontend && node src/services/paxBridge.js'
      );
    }
  }

  /**
   * Check if bridge service is running
   */
  private async checkBridgeStatus(): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`${this.bridgeUrl}/health`, {
        method: 'GET',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Process payment through hardware
   */
  async processPayment(request: PaxHardwarePaymentRequest): Promise<PaxHardwarePaymentResponse> {
    if (!this.isInitialized) {
      throw new Error('PAX Hardware Service not initialized');
    }

    try {
      console.log(`Processing payment: £${request.amount}`);

      const response = await fetch(`${this.bridgeUrl}/payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: request.amount,
          transactionId: request.referenceNumber || this.generateReferenceNumber()
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Payment request failed: ${errorData.error || response.statusText}`);
      }

      const result = await response.json();

      return {
        success: result.success,
        transactionId: result.transactionId,
        authCode: result.authCode,
        cardInfo: {
          last4: result.last4,
          brand: result.cardType,
          entryMethod: 'chip'
        },
        message: result.success ? 'Payment processed successfully' : result.error
      };

    } catch (error) {
      console.error('PAX hardware payment error:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Payment processing failed'
      };
    }
  }

  /**
   * Get hardware status
   */
  async getHardwareStatus(): Promise<PaxHardwareStatus> {
    try {
      const response = await fetch(`${this.bridgeUrl}/status`, {
        method: 'GET',
      });

      if (!response.ok) {
        return {
          connected: false,
          dllLoaded: false,
          terminalReachable: false,
          lastError: `Bridge communication failed: ${response.statusText}`
        };
      }

      const result = await response.json();
      return {
        connected: result.success,
        dllLoaded: true, // Bridge server handles DLL loading
        terminalReachable: result.success,
        lastError: result.error
      };

    } catch (error) {
      return {
        connected: false,
        dllLoaded: false,
        terminalReachable: false,
        lastError: error instanceof Error ? error.message : 'Bridge server not reachable'
      };
    }
  }



  /**
   * Test terminal connection
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${this.bridgeUrl}/api/test`, {
        method: 'POST',
      });

      const result = await response.json();
      return {
        success: result.success,
        message: result.message || (result.success ? 'Connection successful' : 'Connection failed')
      };

    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Connection test failed'
      };
    }
  }

  /**
   * Cancel current transaction
   */
  async cancelTransaction(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${this.bridgeUrl}/api/cancel`, {
        method: 'POST',
      });

      const result = await response.json();
      return {
        success: result.success,
        message: result.message || (result.success ? 'Transaction cancelled' : 'Cancel failed')
      };

    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Cancel failed'
      };
    }
  }

  /**
   * Print receipt directly to thermal printer
   */
  async printReceipt(receiptData: string, copies: number = 1): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${this.bridgeUrl}/api/print`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          receiptData,
          copies
        }),
      });

      const result = await response.json();
      return {
        success: result.success,
        message: result.message || (result.success ? 'Receipt printed' : 'Print failed')
      };

    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Print failed'
      };
    }
  }

  /**
   * Cleanup and shutdown
   */
  async shutdown(): Promise<void> {
    try {
      if (this.bridgeProcess) {
        this.bridgeProcess.kill();
        this.bridgeProcess = null;
      }
      this.isInitialized = false;
    } catch (error) {
      console.error('Error during shutdown:', error);
    }
  }

  /**
   * Check if hardware service is available
   */
  isAvailable(): boolean {
    return this.isInitialized && this.isWindowsEnvironment();
  }



  /**
   * Generate unique reference number
   */
  private generateReferenceNumber(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `HW${timestamp}${random}`;
  }
}

// Export singleton instance
export const paxHardwareService = new PaxHardwareService();
export default paxHardwareService;
