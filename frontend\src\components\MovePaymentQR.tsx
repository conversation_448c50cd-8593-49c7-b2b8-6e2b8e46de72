/**
 * Move Payment QR Component
 *
 * Displays QR code for Move Payment and handles payment status polling
 * Maintains POS terminal UI design with mobile responsiveness
 */

import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { 
  usePollMovePaymentStatus, 
  useMovePaymentTimer, 
  useMovePaymentUtils 
} from '../hooks/useMove';

interface MovePaymentQRProps {
  paymentData: {
    transaction_id: string;
    order_id: string;
    token: string;
    expires_at: string;
    redirect_url: string;
    amount: number;
    currency: string;
  };
  amount: number;
  currency?: string;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  onCancel?: () => void;
  className?: string;
}

export function MovePaymentQR({
  paymentData,
  amount,
  currency = 'EUR',
  onSuccess,
  onError,
  onCancel,
  className = ''
}: MovePaymentQRProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [isGeneratingQR, setIsGeneratingQR] = useState(true);

  const { formatAmount, generateQRCodeUrl, generateMoveAppDeepLink } = useMovePaymentUtils();
  const { isExpired, formattedTime } = useMovePaymentTimer(paymentData.expires_at || undefined);

  // Poll payment status
  const { data: statusData, error: statusError } = usePollMovePaymentStatus(
    paymentData.order_id,
    true,
    (data) => {
      if (data.success && data.data) {
        onSuccess?.(data.data);
      }
    }
  );

  // Generate QR code URL
  useEffect(() => {
    const generateQRCode = async () => {
      try {
        setIsGeneratingQR(true);

        // Check if we have a token - try multiple possible locations
        const token = paymentData.token ||
                     paymentData.metadata?.move_token ||
                     paymentData.metadata?.token ||
                     paymentData.data?.token;
        console.log('Generating QR code for token:', token);
        console.log('Payment data structure:', {
          hasToken: !!paymentData.token,
          hasMetadataMoveToken: !!paymentData.metadata?.move_token,
          hasMetadataToken: !!paymentData.metadata?.token,
          hasDataToken: !!paymentData.data?.token,
          paymentData: paymentData
        });

        if (!token) {
          console.warn('No token found in payment data, using redirect URL directly');
          const qrUrl = generateQRCodeUrl(paymentData.redirect_url, 300);
          setQrCodeUrl(qrUrl);
          setIsGeneratingQR(false);
          return;
        }

        // Get QR code from backend API
        const response = await fetch(`/api/v1/move/payment/${token}/qr-code`);
        console.log('QR code API response status:', response.status);

        if (!response.ok) {
          console.warn('QR code API failed, using redirect URL as fallback');
          const qrUrl = generateQRCodeUrl(paymentData.redirect_url, 300);
          setQrCodeUrl(qrUrl);
          setIsGeneratingQR(false);
          return;
        }

        const result = await response.json();
        console.log('QR code API result:', result);

        if (result.success && result.data && result.data.redirect_url) {
          // Use the QR-optimized URL from the API response
          console.log('Using QR-optimized URL:', result.data.redirect_url);
          const qrUrl = generateQRCodeUrl(result.data.redirect_url, 300);
          setQrCodeUrl(qrUrl);
        } else {
          // Fallback to using the payment data redirect URL
          console.log('Using fallback redirect URL:', paymentData.redirect_url);
          const qrUrl = generateQRCodeUrl(paymentData.redirect_url, 300);
          setQrCodeUrl(qrUrl);
        }

        setIsGeneratingQR(false);
      } catch (error) {
        console.error('Failed to generate QR code:', error);

        // Fallback to using the payment data redirect URL
        console.log('Error fallback - using redirect URL:', paymentData.redirect_url);
        const qrUrl = generateQRCodeUrl(paymentData.redirect_url, 300);
        setQrCodeUrl(qrUrl);
        setIsGeneratingQR(false);
      }
    };

    generateQRCode();
  }, [paymentData.redirect_url, paymentData.token]);

  // Handle expiration
  useEffect(() => {
    if (isExpired) {
      toast.error('Payment has expired');
      onError?.('Payment expired');
    }
  }, [isExpired, onError]);

  // Handle status error
  useEffect(() => {
    if (statusError) {
      console.error('Payment status error:', statusError);
      onError?.('Failed to check payment status');
    }
  }, [statusError, onError]);

  const handleOpenInApp = () => {
    // Try to open Move app via deep link
    const deepLink = generateMoveAppDeepLink(paymentData.token);

    try {
      // Try deep link first
      window.location.href = deepLink;
      toast.success('Opening Move app...');
    } catch (error) {
      console.error('Failed to open Move app:', error);
      toast.error('Could not open Move app. Please scan the QR code manually.');
    }
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(paymentData.redirect_url);
      toast.success('Payment link copied to clipboard');
    } catch (error) {
      console.error('Failed to copy link:', error);
      toast.error('Failed to copy link');
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 font-['Poppins'] mb-2">
          Scan QR Code
        </h2>
        <p className="text-gray-600 font-['Poppins']">
          Use your Move app to complete payment
        </p>
      </div>

      {/* Amount Display */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6 text-center">
        <p className="text-sm text-gray-600 font-['Poppins'] mb-1">Amount to Pay</p>
        <p className="text-3xl font-bold text-gray-900 font-['Poppins']">
          {formatAmount(amount)}
        </p>
        <p className="text-sm text-gray-500 font-['Poppins']">{currency}</p>
      </div>

      {/* Timer Display */}
      <div className="text-center mb-4">
        {isExpired ? (
          <div className="text-red-600 font-semibold font-['Poppins']">
            ⏰ Payment Expired
          </div>
        ) : (
          <div className="text-blue-600 font-semibold font-['Poppins']">
            ⏱️ Expires in: {formattedTime}
          </div>
        )}
      </div>

      {/* QR Code Display */}
      <div className="bg-white border-2 border-gray-200 rounded-lg p-4 mb-6">
        {isGeneratingQR ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600 font-['Poppins']">Generating QR Code...</p>
            </div>
          </div>
        ) : qrCodeUrl ? (
          <div className="text-center">
            <img
              src={qrCodeUrl}
              alt="Move Payment QR Code"
              className="mx-auto mb-4 rounded-lg"
              style={{ maxWidth: '250px', height: 'auto' }}
            />
            <p className="text-sm text-gray-600 font-['Poppins']">
              Scan with your Move app
            </p>
          </div>
        ) : (
          <div className="flex items-center justify-center h-64">
            <p className="text-red-600 font-['Poppins']">Failed to generate QR code</p>
          </div>
        )}
      </div>

      {/* Payment Status */}
      {(statusData as any)?.success && (statusData as any).data && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <p className="text-sm text-blue-800 font-['Poppins']">
            Status: <span className="font-semibold capitalize">{(statusData as any).data.status}</span>
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-3">
        <button
          onClick={handleOpenInApp}
          disabled={isExpired}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 font-['Poppins']"
        >
          Open in Move App
        </button>

        <button
          onClick={handleCopyLink}
          disabled={isExpired}
          className="w-full bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 disabled:cursor-not-allowed text-gray-700 font-semibold py-3 px-4 rounded-lg transition-colors duration-200 font-['Poppins']"
        >
          Copy Payment Link
        </button>

        {onCancel && (
          <button
            onClick={onCancel}
            className="w-full bg-red-200 hover:bg-red-300 text-red-700 font-semibold py-3 px-4 rounded-lg transition-colors duration-200 font-['Poppins']"
          >
            Cancel Payment
          </button>
        )}
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-sm font-semibold text-blue-900 font-['Poppins'] mb-2">
          Payment Instructions:
        </h3>
        <ol className="text-xs text-blue-800 font-['Poppins'] space-y-1">
          <li>1. Open your Move app on your phone</li>
          <li>2. Scan the QR code above or tap "Open in Move App"</li>
          <li>3. Confirm the payment details in your app</li>
          <li>4. Complete the payment</li>
          <li>5. You'll be redirected back automatically</li>
        </ol>
      </div>

      {/* Payment Details */}
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-xs font-semibold text-gray-700 font-['Poppins'] mb-2">
          Payment Details:
        </h4>
        <div className="text-xs text-gray-600 font-['Poppins'] space-y-1">
          <div>Order ID: {paymentData.order_id}</div>
          <div>Transaction ID: {paymentData.transaction_id}</div>
        </div>
      </div>

      {/* Move Payment Branding */}
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500 font-['Poppins']">
          Secured by Move Payment Gateway
        </p>
      </div>
    </div>
  );
}
